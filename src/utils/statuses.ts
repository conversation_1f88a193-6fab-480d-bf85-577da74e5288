import {SaleStatus} from '@/types/global';

export type ComputedStatus = StatusDetails & {
  value: StatusKey;
};

export type StatusConfig = {
  statuses: Record<StatusKey, StatusDetails>;
  transitions: Record<StatusKey, StatusKey[]>;
};

export type StatusDetails = {
  action?: string;
  color: string;
};

export type StatusHelperResult = {
  available: ComputedStatus[];
  current: ComputedStatus;
};

export type StatusKey = string;

// export const saleActions = {
//   [SaleStatus.CANCELED as string]: 'cancel',
//   [SaleStatus.DELIVERED as string]: 'delivered',
//   [SaleStatus.ON_HOLD as string]: 'on-hold',
//   [SaleStatus.PROCESSING as string]: 'processing',
//   [SaleStatus.QUOTE_SENT as string]: 'quote-sent',
//   [SaleStatus.READY_TO_SHIP as string]: 'ready-to-ship',
//   [SaleStatus.SHIPPING as string]: 'ship',
//   [SaleStatus.SUBMITTED as string]: 'confirm',
// };
//
// export const manufacturingActions = {
//   [ManufacturingStatus.ACCOUNTED as string]: 'accounted',
//   [ManufacturingStatus.CONSUMPTION_RECORDED as string]: 'record-consumption',
// };
//
// export const taskActions = {
//   [ManufacturingTaskStatus.DONE as string]: 'done',
//   [ManufacturingTaskStatus.IN_PROGRESS as string]: 'in-progress',
//   [ManufacturingTaskStatus.STOPPED as string]: 'stopped',
// };
//
// export const purchaseActions = {
//   [PurchaseStatus.DELIVERED as string]: 'delivered',
//   [PurchaseStatus.SENT_FOR_QUOTE as string]: 'send-for-quote',
//   [PurchaseStatus.SENT as string]: 'sent',
// };
//
// export const servicingActions = {
//   [ServicingStatus.BLOCKED as string]: 'blocked',
//   [ServicingStatus.CLOSED as string]: 'closed',
//   [ServicingStatus.EXECUTED as string]: 'executed',
//   [ServicingStatus.IN_PROGRESS as string]: 'in-progress',
// };

const saleOrderStatuses: StatusConfig = {
  statuses: {
    [SaleStatus.CANCELED]: {
      action: 'cancel',
      color: 'error',
    },
    [SaleStatus.DELIVERED]: {
      action: 'delivered',
      color: 'success',
    },
    [SaleStatus.ON_HOLD]: {
      action: 'on-hold',
      color: 'error',
    },
    [SaleStatus.PICKING_PACKING]: {
      color: 'info',
    },
    [SaleStatus.PROCESSING]: {
      action: 'processing',
      color: 'info',
    },
    [SaleStatus.READY_TO_SHIP]: {
      action: 'ready-to-ship',
      color: 'warning',
    },
    [SaleStatus.SHIPPING]: {
      action: 'ship',
      color: 'info',
    },
    [SaleStatus.SUBMITTED]: {
      action: 'confirm',
      color: 'secondary',
    },
  },
  transitions: {
    [SaleStatus.CANCELED]: [SaleStatus.CANCELED],
    [SaleStatus.DELIVERED]: [SaleStatus.DELIVERED],
    [SaleStatus.ON_HOLD]: [
      SaleStatus.SUBMITTED,
      SaleStatus.PROCESSING,
      SaleStatus.READY_TO_SHIP,
      SaleStatus.SHIPPING,
      SaleStatus.DELIVERED,
      SaleStatus.CANCELED,
    ],
    [SaleStatus.PICKING_PACKING]: [
      SaleStatus.READY_TO_SHIP,
      SaleStatus.SHIPPING,
      SaleStatus.DELIVERED,
      SaleStatus.CANCELED,
      SaleStatus.ON_HOLD,
    ],
    [SaleStatus.PROCESSING]: [SaleStatus.CANCELED, SaleStatus.ON_HOLD],
    [SaleStatus.READY_TO_SHIP]: [SaleStatus.SHIPPING, SaleStatus.DELIVERED, SaleStatus.CANCELED, SaleStatus.ON_HOLD],
    [SaleStatus.SHIPPING]: [SaleStatus.DELIVERED, SaleStatus.CANCELED, SaleStatus.ON_HOLD],
    [SaleStatus.SUBMITTED]: [SaleStatus.PROCESSING, SaleStatus.CANCELED, SaleStatus.ON_HOLD],
  },
};

const saleQuoteStatuses: StatusConfig = {
  statuses: {
    [SaleStatus.IN_QUOTATION]: {
      color: 'secondary',
    },
    [SaleStatus.QUOTE_SENT]: {
      action: 'quote-sent',
      color: 'secondary',
    },
  },
  transitions: {
    [SaleStatus.IN_QUOTATION]: [SaleStatus.QUOTE_SENT, SaleStatus.CANCELED],
    [SaleStatus.QUOTE_SENT]: [SaleStatus.CANCELED],
  },
};

const manufacturingTaskStatuses: StatusConfig = {
  statuses: {},
  transitions: {},
};

const manufacturingOrderStatuses: StatusConfig = {
  statuses: {},
  transitions: {},
};

const servicingOrderStatuses: StatusConfig = {
  statuses: {},
  transitions: {},
};

const purchaseOrderStatuses: StatusConfig = {
  statuses: {},
  transitions: {},
};

export const getStatuses = (currentStatus: StatusKey, config: StatusConfig): StatusHelperResult | undefined => {
  const currentDetails = config.statuses[currentStatus];
  if (!currentDetails) return undefined;

  const allowedStatuses = config.transitions[currentStatus] || [];
  if (!allowedStatuses || allowedStatuses.length === 0) return undefined;

  const available = allowedStatuses
    .map((statusKey): ComputedStatus | null => {
      const status = config.statuses[statusKey];
      if (!status) return null;

      return {
        action: status.action,
        color: status.color,
        value: statusKey,
      };
    })
    .filter((s): s is ComputedStatus => s !== null);

  return {
    available,
    current: {
      action: currentDetails.action,
      color: currentDetails.color,
      value: currentStatus,
    },
  };
};
