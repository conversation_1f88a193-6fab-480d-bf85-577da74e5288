import {useCallback} from 'react';

import axios from 'axios';
import {useAtomValue} from 'jotai';
import {first} from 'lodash';
import {useTranslations} from 'next-intl';
import {UseFormSetValue} from 'react-hook-form';
import {useSWRConfig} from 'swr';

import {toast} from '@/components/ui/Toast';
import {defaultCurrencyAtom, defaultVATAtom} from '@/store/defaults';
import {SaleItem, SaleOrder, SaleType} from '@/types/sales';
import {handleError} from '@/utils/axios';
import {parseCurrency} from '@/utils/common';
import {saleActions} from 'hooks/helpers/actions';
import {AccompanyingNoteItem, GoodsAccompanyingNote, SaleStatus} from 'types/global';

const useSaleOrderActions = () => {
  const t = useTranslations();
  const defaultCurrency = useAtomValue(defaultCurrencyAtom);
  const defaultVAT = useAtomValue(defaultVATAtom);
  const {mutate: globalMutate} = useSWRConfig();

  const prepareData = useCallback((values: any) => {
    if (!values) return {} as SaleOrder;
    return {
      ...values,
      discountAmount: parseCurrency(values.discountAmount, false),
      items: (values?.items || []).map((item: SaleItem) => ({
        ...item,
        administrativeOverheadCosts: parseCurrency(item.administrativeOverheadCosts, false),
        discount: item.discount * 100,
        discountAmount: parseCurrency(item.discountAmount, false),
        laborCosts: parseCurrency(item.laborCosts, false),
        lastPurchase: item.lastPurchase
          ? {
              ...item.lastPurchase,
              price: parseCurrency(item.lastPurchase.price, false),
            }
          : item.lastPurchase,
        materialCosts: parseCurrency(item.materialCosts, false),
        originalQuantity: item.quantity,
        price: parseCurrency(item.price, false),
        taxAmount: parseCurrency(item.taxAmount, false),
        totalAmount: parseCurrency(item.totalAmount, false),
      })),
      subTotalAmount: parseCurrency(values.subTotalAmount, false),
      taxAmount: parseCurrency(values.taxAmount, false),
      totalAmount: parseCurrency(values.totalAmount, false),
    } as SaleOrder;
  }, []);

  const processValues = useCallback(
    (values: Partial<SaleOrder>) => ({
      ...values,
      customerId: values.customer?.id,
      isQuotation: values.status === SaleStatus.IN_QUOTATION,
      items: (values.items || []).map((item) => ({
        ...item,
        discount: item.discount / 100 || 0,
        discountAmount: parseCurrency({
          amount: item.discountAmount.amount,
          currency: item.discountAmount.currency || defaultCurrency,
        }),
        price: parseCurrency({
          amount: item.price.amount,
          currency: item.price.currency || defaultCurrency,
        }),
        taxAmount: parseCurrency({
          amount: item.taxAmount.amount,
          currency: item.taxAmount.currency || defaultCurrency,
        }),
        totalAmount: parseCurrency({
          amount: item.totalAmount.amount,
          currency: item.totalAmount.currency || defaultCurrency,
        }),
      })),
      offerDate: values.offerDate || new Date().toISOString(),
    }),
    [defaultCurrency],
  );

  const createSaleOrder = useCallback(
    (type: SaleType, values: SaleOrder) => {
      const newValues = processValues(values);

      return axios
        .post('/api/sales/orders/create', newValues)
        .then((res) => res.data)
        .then((data) => {
          toast.success(
            t('name has been created successfully', {
              created: t('created.female'),
              name: `${type === SaleType.QUOTE ? t('suffixed.quote.start') : t('suffixed.order.start')} ${data.number}`,
            }),
          );

          globalMutate((key) => Array.isArray(key) && key[0] === 'sales');

          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to create successfully', {
              created: t('created.female'),
              name: type === SaleType.QUOTE ? t('suffixed.quote.start') : t('suffixed.order.start'),
            }),
          ),
        );
    },
    [globalMutate, processValues, t],
  );

  const updateSaleOrder = useCallback(
    async (id: string, type: SaleType, values: SaleOrder, initialStatus?: SaleStatus) => {
      const newValues = processValues(values);
      const statusChanged = initialStatus !== newValues.status;
      let errorOccurred = false;

      if (![SaleStatus.DELIVERED, SaleStatus.SHIPPING].includes(newValues.status as SaleStatus))
        await axios
          .post(`/api/sales/orders/${id}/update`, {...newValues, status: initialStatus})
          .then(() => {
            if (!statusChanged) {
              toast.success(
                t('name has been updated successfully', {
                  name: `${type === SaleType.QUOTE ? t('suffixed.quote.start') : t('suffixed.order.start')} ${
                    newValues.number
                  }`,
                  updated: t('updated.female'),
                }),
              );
            }
          })
          .catch((error) => {
            errorOccurred = true;
            handleError(
              error,
              t('name has failed to update successfully', {
                name: `${type === SaleType.QUOTE ? t('suffixed.quote.start') : t('suffixed.order.start')} ${
                  newValues.number
                }`,
                updated: t('updated.female'),
              }),
            );
          });

      if (!errorOccurred && statusChanged && saleActions[values.status])
        await axios
          .post(`/api/sales/orders/${id}/${saleActions[values.status]}`)
          .then(() => {
            toast.success(
              t('name has been updated successfully', {
                name: `${t('suffixed.order.start')} ${newValues.number}`,
                updated: t('updated.female'),
              }),
            );
          })
          .catch((error) =>
            handleError(
              error,
              t('name has failed to update successfully', {
                name: `${type === SaleType.QUOTE ? t('suffixed.quote.start') : t('suffixed.order.start')} ${
                  newValues.number
                }`,
                updated: t('updated.female'),
              }),
            ),
          );

      if (!errorOccurred) {
        return globalMutate((key) => Array.isArray(key) && key[0] === 'sales');
      }

      return Promise.reject();
    },
    [globalMutate, processValues, t],
  );

  const updateSaleOrderStatus = useCallback(
    (item: Partial<SaleOrder>, status: SaleStatus) => {
      if (saleActions[status])
        axios
          .post(`/api/sales/orders/${item.id}/${saleActions[status]}`)
          .then(() => {
            toast.success(
              t('name has been updated successfully', {
                name: `${t('suffixed.order.start')} ${item.number}`,
                updated: t('updated.female'),
              }),
            );
            globalMutate(
              (key) => Array.isArray(key) && (key[0] === 'sales' || (key[0] === 'sale' && key[1] === item.id)),
            );
          })
          .catch((error) =>
            handleError(
              error,
              t('name has failed to update successfully', {
                name: `${t('suffixed.order.start')} ${item.number}`,
                updated: t('updated.female'),
              }),
            ),
          );
    },
    [globalMutate, t],
  );

  const quoteSentSaleOrder = useCallback(
    (id: string) =>
      axios
        .post(`/api/sales/orders/${id}/quote-sent`)
        .then((res) => res.data)
        .then((data) => {
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.order.start')} ${data.number}`,
              updated: t('updated.female'),
            }),
          );

          globalMutate((key) => Array.isArray(key) && (key[0] === 'sales' || (key[0] === 'sale' && key[1] === id)));

          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: t('suffixed.order.start'),
              updated: t('updated.female'),
            }),
          ),
        ),
    [globalMutate, t],
  );

  const onHoldSaleOrder = useCallback(
    (id: string) =>
      axios
        .post(`/api/sales/orders/${id}/on-hold`)
        .then((res) => res.data)
        .then((data) => {
          toast.success(
            t('name has been updated successfully', {
              name: `${t('suffixed.order.start')} ${data.number}`,
              updated: t('updated.female'),
            }),
          );

          globalMutate((key) => Array.isArray(key) && (key[0] === 'sales' || (key[0] === 'sale' && key[1] === id)));

          return data;
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: t('suffixed.order.start'),
              updated: t('updated.female'),
            }),
          ),
        ),
    [globalMutate, t],
  );

  const updateSaleOrderRank = useCallback(
    (value?: string[]) => {
      if (!value) return;

      axios
        .post('/api/sales/orders/ranking', value)
        .then(() => {
          toast.success(
            t('name has been updated successfully', {
              name: t('order ranking'),
              updated: t('updated.female'),
            }),
          );
          globalMutate((key) => Array.isArray(key) && key[0] === 'sales');
        })
        .catch((error) =>
          handleError(
            error,
            t('name has failed to update successfully', {
              name: t('order ranking'),
              updated: t('updated.female'),
            }),
          ),
        );
    },
    [globalMutate, t],
  );

  const calculateSaleOrderSummary = useCallback(
    (items: SaleItem[], setValue: UseFormSetValue<SaleOrder>) => {
      let summarySubtotal = 0;
      let summaryDiscount = 0;
      let summaryVat = 0;
      let summaryTotal = 0;
      const currency = first(items)?.price?.currency || defaultCurrency;

      items.forEach((item) => {
        const {discountAmount, price, quantity = 1} = item as Partial<SaleItem>;

        const discount = discountAmount?.amount || 0;
        const vat =
          ((price?.amount || 0) * quantity - discount) *
          (item.vatRate !== null && item.vatRate !== undefined ? item.vatRate : defaultVAT);
        const total = (price?.amount || 0) * quantity - discount + vat;

        summarySubtotal += (price?.amount || 0) * quantity;
        summaryDiscount += discount;
        summaryVat += vat;
        summaryTotal += total;
      });

      setValue('subTotalAmount', {amount: summarySubtotal, currency});
      setValue('discountAmount', {amount: summaryDiscount, currency});
      setValue('taxAmount', {amount: summaryVat, currency});
      setValue('totalAmount', {amount: summaryTotal, currency});
    },
    [defaultCurrency, defaultVAT],
  );

  const getAvailableStatuses = useCallback(
    (
      status: SaleStatus,
      {withoutNext = false, withoutSelf = false}: {withoutNext?: boolean; withoutSelf?: boolean} = {},
    ) => {
      const statuses = [];

      switch (status) {
        case SaleStatus.CANCELED:
          if (!withoutSelf) statuses.push(SaleStatus.CANCELED);
          break;
        case SaleStatus.DELIVERED:
          if (!withoutSelf) statuses.push(SaleStatus.DELIVERED);
          break;
        case SaleStatus.IN_QUOTATION:
          if (!withoutSelf) statuses.push(SaleStatus.IN_QUOTATION);
          if (!withoutNext) statuses.push(SaleStatus.QUOTE_SENT);
          statuses.push(SaleStatus.CANCELED);
          break;
        case SaleStatus.ON_HOLD:
          if (!withoutSelf) statuses.push(SaleStatus.ON_HOLD);
          break;
        case SaleStatus.PICKING_PACKING:
          if (!withoutSelf) statuses.push(SaleStatus.PICKING_PACKING);
          if (!withoutNext) statuses.push(SaleStatus.READY_TO_SHIP);
          statuses.push(SaleStatus.CANCELED);
          break;
        case SaleStatus.PROCESSING:
          if (!withoutSelf) statuses.push(SaleStatus.PROCESSING);
          statuses.push(SaleStatus.CANCELED);
          break;
        case SaleStatus.QUOTE_SENT:
          if (!withoutSelf) statuses.push(SaleStatus.QUOTE_SENT);
          if (!withoutNext) statuses.push(SaleStatus.SUBMITTED);
          statuses.push(SaleStatus.CANCELED);
          break;
        case SaleStatus.READY_TO_SHIP:
          if (!withoutSelf) statuses.push(SaleStatus.READY_TO_SHIP);
          if (!withoutNext) statuses.push(SaleStatus.SHIPPING);
          statuses.push(SaleStatus.DELIVERED, SaleStatus.CANCELED);
          break;
        case SaleStatus.SHIPPING:
          if (!withoutSelf) statuses.push(SaleStatus.SHIPPING);
          if (!withoutNext) statuses.push(SaleStatus.DELIVERED);
          break;
        case SaleStatus.SUBMITTED:
          if (!withoutSelf) statuses.push(SaleStatus.SUBMITTED);
          if (!withoutNext) statuses.push(SaleStatus.PROCESSING);
          statuses.push(SaleStatus.CANCELED);
          break;
        default:
          break;
      }

      return statuses;
    },
    [],
  );

  const getRemainingNoteItems = useCallback(
    (items: SaleItem[], notes: GoodsAccompanyingNote[], existingItems?: AccompanyingNoteItem[]) => {
      if (!items) return [];

      return items
        .map((saleItem) => {
          const totalProcessedQuantity = notes
            .flatMap((note) => note.items)
            .filter((noteItem) => noteItem.id === saleItem.productId)
            .reduce((sum, noteItem) => sum + noteItem.quantity, 0);

          const existingItem = existingItems?.find((item) => item.id === saleItem.productId);

          const remainingQuantity = (saleItem.quantity || 0) - totalProcessedQuantity + (existingItem?.quantity || 0);

          return {
            ...saleItem,
            remainingQuantity,
          };
        })
        .filter((item) => item.remainingQuantity > 0 && item.productId);
    },
    [],
  );

  return {
    calculateSaleOrderSummary,
    createSaleOrder,
    getAvailableStatuses,
    getRemainingNoteItems,
    onHoldSaleOrder,
    prepareData,
    processValues,
    quoteSentSaleOrder,
    updateSaleOrder,
    updateSaleOrderRank,
    updateSaleOrderStatus,
  };
};

export default useSaleOrderActions;
