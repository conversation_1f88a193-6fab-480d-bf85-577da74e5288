import {FC, useEffect, useMemo, useState} from 'react';

import {closestCorners, DndContext, DragEndEvent, PointerSensor, useSensor, useSensors} from '@dnd-kit/core';
import {arrayMove, SortableContext, verticalListSortingStrategy} from '@dnd-kit/sortable';
import {useAtomValue} from 'jotai';
import {useTranslations} from 'next-intl';

import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableHeadActions,
  TableHeader,
  TableRow,
} from '@/components/ui/Table';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import useHeight from '@/hooks/helpers/useHeight';
import useSaleOrderActions from '@/hooks/useSaleOrderActions';
import useSaleOrders from '@/hooks/useSaleOrders';
import {SaleStatus} from '@/types/global';
import {SaleType} from '@/types/sales';

import {salesSearchQueryAtom, SalesView} from './salesListStore';
import SalesListTableRow from './SalesListTableRow';

type Props = {
  customerId?: string;
  disableHeight?: boolean;
  preview?: boolean;
  saleType: SaleType;
  view: SalesView;
};

const SalesListTable: FC<Props> = ({customerId, disableHeight, preview, saleType, view}) => {
  const {elementRef} = useHeight();
  const statuses = useMemo(() => {
    let defaultValue: SaleStatus[] = [];
    switch (view) {
      case SalesView.active:
        if (saleType === SaleType.ORDER) {
          defaultValue = [
            SaleStatus.SUBMITTED,
            SaleStatus.SHIPPING,
            SaleStatus.READY_TO_SHIP,
            SaleStatus.PROCESSING,
            SaleStatus.PICKING_PACKING,
          ];
        } else {
          defaultValue = [SaleStatus.IN_QUOTATION, SaleStatus.QUOTE_SENT];
        }
        break;
      case SalesView.canceled:
        defaultValue = [SaleStatus.CANCELED];
        break;
      case SalesView.delivered:
        defaultValue = [SaleStatus.DELIVERED];
        break;
    }
    return defaultValue;
  }, [saleType, view]);
  const search = useAtomValue(salesSearchQueryAtom(saleType));
  const sort = useMemo(
    () => ([SalesView.all, SalesView.canceled, SalesView.delivered].includes(view) ? ['-updateTime'] : []),
    [view],
  );
  const {isLoading, saleOrders} = useSaleOrders({customerId, search, sort: sort as any, statuses});
  const [orders, setOrders] = useState(saleOrders);
  const {updateSaleOrderRank} = useSaleOrderActions();
  const {hasPermission, isLoading: permissionIsLoading} = useHasPermission();
  const t = useTranslations();

  useEffect(() => {
    setOrders(saleOrders);
  }, [saleOrders]);

  const sensors = useSensors(useSensor(PointerSensor));

  const handleDragEnd = ({active, over}: DragEndEvent) => {
    if (active.id !== over?.id) {
      const oldIndex = saleOrders?.findIndex((order) => order.id === active.id);
      const newIndex = over ? saleOrders?.findIndex((order) => order.id === over?.id) : -1;
      if (oldIndex !== undefined && newIndex !== undefined && oldIndex !== -1 && newIndex !== -1) {
        const newOrders = arrayMove(saleOrders, oldIndex, newIndex);
        updateSaleOrderRank(newOrders.map((order) => order.id));
        setOrders(newOrders);
      }
    }
  };

  return (
    <TableContainer ref={!disableHeight ? elementRef : null}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('order#')}</TableHead>
            <TableHead>{t('order date')}</TableHead>
            {!customerId && <TableHead>{t('customer')}</TableHead>}
            {hasPermission('financial', 'sales') && <TableHead className='text-right'>{t('total value')}</TableHead>}
            <TableHead>
              {t(
                view === SalesView.active
                  ? saleType === SaleType.ORDER
                    ? 'delivery deadline'
                    : 'quote expiration'
                  : view === SalesView.delivered
                    ? 'delivered date'
                    : 'cancel date',
              )}
            </TableHead>
            <TableHead className='text-right'>{t('available', {isPlural: 'false'})}</TableHead>
            <TableHead className='text-right'>{t('order status')}</TableHead>
            {(saleType !== SaleType.QUOTE || view === SalesView.canceled) && hasPermission('financial', 'sales') && (
              <TableHeadActions className='text-right'>{t('orders')}</TableHeadActions>
            )}
          </TableRow>
        </TableHeader>
        <TableBody className='overflow-y-hidden' isValidating={isLoading || permissionIsLoading}>
          <DndContext collisionDetection={closestCorners} onDragEnd={handleDragEnd} sensors={sensors}>
            <SortableContext items={orders} strategy={verticalListSortingStrategy}>
              {orders?.map((order, index) => (
                <SalesListTableRow
                  customerId={customerId}
                  key={`${order.id}-${saleType}-row-${index}`}
                  order={order}
                  preview={preview}
                  saleType={saleType}
                  view={view}
                />
              ))}
            </SortableContext>
          </DndContext>
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default SalesListTable;
