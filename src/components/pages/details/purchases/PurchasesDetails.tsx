import {FC} from 'react';

import {ArrowLeftIcon, EllipsisVerticalIcon, ReceiptIcon, ReceiptTextIcon} from 'lucide-react';
import {useTranslations} from 'next-intl';
import {FormProvider} from 'react-hook-form';

import {detailsDataShownAtom} from '@/components/pages/details/detailsStore';
import {Button} from '@/components/ui/Button';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from '@/components/ui/DropdownMenu';
import ActivityButton from '@/components/ui/special/ActivityButton';
import {HideableContentToggle} from '@/components/ui/special/HideableContentToggle';
import {Link} from '@/components/ui/special/Link';
import {Page, PageContent, PageHeader, PageHeaderTitle, PageTitle} from '@/components/ui/special/Page';
import StatusButton from '@/components/ui/special/StatusButton';
import {useHasPermission} from '@/hooks/helpers/useHasPermission';
import {useRouter} from '@/hooks/helpers/useRouter';
import useActivities from '@/hooks/useActivities';
import usePurchaseOrder from '@/hooks/usePurchaseOrder';
import {ActivityType, PurchaseStatus} from '@/types/global';

import PurchasesDetailsData from './PurchasesDetailsData';
import PurchasesDetailsTable from './PurchasesDetailsTable';

type Props = {
  id: string;
};

const PurchasesDetails: FC<Props> = ({id}) => {
  const t = useTranslations();
  const {
    cancelPurchaseOrder,
    isDirty,
    isLoading,
    savePurchaseOrder,
    useFormActions: {
      formState: {errors, ...restUseFormState},
      register,
      resetField,
      watch,
      ...restUseFormActions
    },
  } = usePurchaseOrder(id);
  const {activities, createActivity} = useActivities(id, ActivityType.PURCHASE);
  const {hasPermission, isLoading: permissionIsLoading} = useHasPermission();
  const {back, push} = useRouter();

  if (isLoading || permissionIsLoading) return null;

  return (
    <Page>
      <PageTitle>{`${watch('number')} - ${t('purchases')}`}</PageTitle>
      <PageHeader>
        <PageHeaderTitle className='inline-flex items-center gap-2'>
          <Button onClick={() => back('/purchases')} size='icon' variant='none'>
            <ArrowLeftIcon />
          </Button>
          <div className='flex flex-col'>
            {watch('number')}
            <div className='text-sm font-normal'>{t(watch('status'))}</div>
          </div>
        </PageHeaderTitle>
        <HideableContentToggle store={detailsDataShownAtom(watch('id'))} />
        <div className='grow' />
        {watch('id') && <ActivityButton activities={activities} createActivity={createActivity} />}
        {isDirty && <Button onClick={savePurchaseOrder}>{t('save')}</Button>}
        {!isDirty && (
          <>
            {watch('receptionReceipt.id') && (
              <Button asChild variant='secondary'>
                <Link href={`/inventory/receptions/${watch('receptionReceipt.id')}`}>
                  <ReceiptTextIcon strokeWidth={1} /> {t('view reception')}
                </Link>
              </Button>
            )}
            <Button asChild variant='secondary'>
              <Link href={`/purchases/orders/${id}/invoice`}>
                <ReceiptIcon strokeWidth={1} /> {t('documents')}
              </Link>
            </Button>
            {watch('status') === PurchaseStatus.SUBMITTED &&
              hasPermission('financial', 'purchases') &&
              !watch('receptionReceipt.id') && (
                <StatusButton
                  nextStatus={'mark as delivered'}
                  onChange={() => push(`/purchases/orders/${id}/reception`)}
                />
              )}
          </>
        )}
        {watch('status') === PurchaseStatus.SUBMITTED && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className='px-1.5' variant='ghost'>
                <EllipsisVerticalIcon />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuItem onSelect={cancelPurchaseOrder}>{t('cancel order')}</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </PageHeader>
      <PageContent>
        <FormProvider
          {...{formState: {errors, ...restUseFormState}, register, resetField, watch, ...restUseFormActions}}
        >
          <PurchasesDetailsData />
          <PurchasesDetailsTable />
        </FormProvider>
      </PageContent>
    </Page>
  );
};

export default PurchasesDetails;
